import { getAllSubmissions, getPresignedUrl } from "../controllers/aegisGraderController.js";
import { verifyJWT } from "../middleware/verifyJWT.js";
import { checkAegisGraderCredits } from "../middleware/creditCheck.js";

import express from "express";

const router = express.Router();

// Apply JWT verification to all routes
router.use(verifyJWT);

// Legacy route (commented out since submitForGrading is no longer used)
// router.post("/submit", checkAegisGraderCredits, submitForGrading);
router.get("/submissions/:userId", getAllSubmissions);

// Apply credit check to presigned URL generation (new SQS/Lambda workflow)
router.post("/getPresigned", checkAegisGraderCredits, getPresignedUrl);

export default router;
