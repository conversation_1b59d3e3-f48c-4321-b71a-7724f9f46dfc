import mongoose from "mongoose";

const aegisGraderSchema = new mongoose.Schema({
    testDetails: {
        createdBy: { type: String, required: true },
        className: { type: String, required: true },
        subject: { type: String, required: true },
        date: { type: String, required: true },
    },
    answerSheets: [
        {
            id: { type: String, required: true },
            studentName: { type: String, required: true },
            rollNumber: { type: String, required: true },
            pdfUrl: { type: String, required: true },
            timestamp: { type: Number, required: true },
            className: { type: String, required: true },
            evaluationResult: { type: mongoose.Schema.Types.Mixed }
        },
    ],
    questionPaper: {
        type: {
            type: String,
            enum: ['rubric', 'questionPaper'],
            required: false,
        },
        pdfUrl: { type: String, required: false },
        timestamp: { type: Number, required: false },
    },
    rubric: {
        type: {
            type: String,
            enum: ['rubric', 'questionPaper'],
            required: false,
        },
        pdfUrl: { type: String, required: false },
        timestamp: { type: Number, required: false },
    }
}, { collection: "AegisGrader", timestamps: true });

aegisGraderSchema.index({ timestamp: -1 });

const AegisGrader = mongoose.model("AegisGrader", aegisGraderSchema);
export default AegisGrader;