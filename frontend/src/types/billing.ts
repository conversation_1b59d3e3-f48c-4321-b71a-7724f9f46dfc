// Billing and Payment Types for AegisGrader

export interface PaymentTransaction {
    _id: string;
    transactionId: string;
    type: 'PURCHASE' | 'USAGE' | 'REFUND' | 'BONUS' | 'INITIAL_GRANT';
    status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
    creditAmount: number;
    balanceBefore: number;
    balanceAfter: number;
    description: string;
    createdAt: string;
    updatedAt: string;
    completedAt?: string;
    payment?: PaymentDetails;
    usage?: UsageDetails;
    notes?: string;
    ipAddress?: string;
    userAgent?: string;
}

export interface PaymentDetails {
    razorpayOrderId: string;
    razorpayPaymentId: string;
    razorpaySignature: string;
    amount: number; // Amount in paisa
    currency: string;
    packageType: string; // e.g., 'BASIC_10', 'STANDARD_50', 'PREMIUM_100'
    packageName: string; // e.g., '10 Credits', '50 Credits', '100 Credits'
}

export interface UsageDetails {
    feature: string;
    description: string;
    relatedId: string;
    metadata?: {
        testDetails?: {
            className: string;
            subject: string;
            date: string;
        };
        answerSheetCount?: number;
        evaluationCount?: number;
    };
}

export interface CreditBalance {
    currentBalance: number;
    totalEarned: number;
    totalSpent: number;
    lastUpdated: string;
}

export interface BillingInfo {
    customerId?: string;
    lastPurchaseDate?: string;
    totalAmountSpent: number; // Total money spent in paisa
}

export interface TransactionFilters {
    type?: 'PURCHASE' | 'USAGE' | 'REFUND' | 'BONUS' | 'INITIAL_GRANT';
    status?: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
    dateFrom?: string;
    dateTo?: string;
    feature?: string;
    page?: number;
    limit?: number;
}

export interface UsageAnalytics {
    totalCreditsUsed: number;
    totalEvaluations: number;
    averageCreditsPerDay: number;
    mostUsedFeature: string;
    usageByFeature: {
        [feature: string]: {
            count: number;
            credits: number;
            percentage: number;
        };
    };
    dailyUsage: {
        date: string;
        credits: number;
        evaluations: number;
    }[];
    weeklyUsage: {
        week: string;
        credits: number;
        evaluations: number;
    }[];
    monthlyUsage: {
        month: string;
        credits: number;
        evaluations: number;
    }[];
}

export interface PaymentAnalytics {
    totalAmountSpent: number;
    totalCreditsPurchased: number;
    averageOrderValue: number;
    mostPopularPackage: string;
    purchasesByPackage: {
        [packageType: string]: {
            count: number;
            totalAmount: number;
            totalCredits: number;
            percentage: number;
        };
    };
    monthlyPurchases: {
        month: string;
        amount: number;
        credits: number;
        transactions: number;
    }[];
}

export interface BillingDashboardData {
    creditBalance: CreditBalance;
    billingInfo: BillingInfo;
    recentTransactions: PaymentTransaction[];
    usageAnalytics: UsageAnalytics;
    paymentAnalytics: PaymentAnalytics;
    totalTransactions: number;
}

export interface TransactionHistoryResponse {
    success: boolean;
    data: {
        transactions: PaymentTransaction[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    };
}

export interface AnalyticsResponse {
    success: boolean;
    data: {
        usageAnalytics: UsageAnalytics;
        paymentAnalytics: PaymentAnalytics;
    };
}

export interface PaymentReceipt {
    transactionId: string;
    paymentId: string;
    orderId: string;
    amount: number;
    currency: string;
    packageName: string;
    creditsAdded: number;
    paymentDate: string;
    paymentMethod: string;
    status: string;
    customerInfo: {
        name: string;
        email: string;
        userId: string;
    };
}

// Component Props Types
export interface PaymentHistoryProps {
    className?: string;
    showFilters?: boolean;
    limit?: number;
    onTransactionClick?: (transaction: PaymentTransaction) => void;
}

export interface CreditUsageProps {
    className?: string;
    showAnalytics?: boolean;
    timeRange?: 'week' | 'month' | 'quarter' | 'year';
}

export interface BillingStatsProps {
    className?: string;
    showDetailedStats?: boolean;
}

export interface TransactionFilterProps {
    filters: TransactionFilters;
    onFiltersChange: (filters: TransactionFilters) => void;
    onReset: () => void;
    className?: string;
}

export interface ReceiptModalProps {
    isOpen: boolean;
    onClose: () => void;
    transaction: PaymentTransaction;
    onDownload?: () => void;
}
