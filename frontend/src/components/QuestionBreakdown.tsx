import React from "react";

// --- Types for Question Breakdown ---
interface CriterionBreakdown {
    criterion: string;
    score: string;
    maxScore?: string;
}

interface QuestionBreakdown {
    questionNumber: string;
    marksAwarded: number;
    marksPossible: number;
    percentage: number;
    feedback: string;
    criteriaBreakdown: CriterionBreakdown[];
}

interface EvaluationBreakdown {
    totalMarks: number;
    maxMarks: number;
    overallPercentage: number;
    questions: QuestionBreakdown[];
}

// --- Question Breakdown Function ---
export const parseQuestionBreakdown = (evaluationData: any): EvaluationBreakdown | null => {
    try {
        // Handle different input formats
        let xmlString = '';

        if (Array.isArray(evaluationData) && evaluationData.length > 0) {
            // Current format: Array with markdown/XML string
            xmlString = evaluationData[0];
        } else if (typeof evaluationData === 'string') {
            // Direct XML string
            xmlString = evaluationData;
        } else {
            console.error('Invalid evaluation data format:', typeof evaluationData, evaluationData);
            return null;
        }

        // Extract XML content from markdown if needed
        const xmlMatch = xmlString.match(/<evaluation>([\s\S]*?)<\/evaluation>/);
        if (!xmlMatch) {
            console.error('No evaluation XML found in data');
            return null;
        }

        const xmlContent = `<evaluation>${xmlMatch[1]}</evaluation>`;

        // Clean the XML string to remove any malformed characters
        const cleanedXmlContent = xmlContent
            .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
            .replace(/&(?![a-zA-Z0-9#]{1,6};)/g, '&amp;') // Escape unescaped ampersands
            .trim();

        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(cleanedXmlContent, 'text/xml');

        // Check for parser errors
        const parserError = xmlDoc.querySelector('parsererror');
        if (parserError) {
            console.error('XML parsing error:', parserError.textContent);
            return null;
        }

        const evaluation = xmlDoc.querySelector('evaluation');
        if (!evaluation) {
            console.error('No evaluation element found in parsed XML');
            return null;
        }

        // Extract overall evaluation data
        const totalMarks = parseInt(evaluation.querySelector('total_marks')?.textContent || '0');
        const maxMarks = parseInt(evaluation.querySelector('maximum_possible_marks')?.textContent || '0');
        const overallPercentage = parseInt(evaluation.querySelector('percentage_score')?.textContent || '0');

        // Extract question details
        const questionElements = evaluation.querySelectorAll('question');

        const questions: QuestionBreakdown[] = Array.from(questionElements).map(question => {
            const questionNumber = question.getAttribute('number') || question.querySelector('number')?.textContent || '';
            const marksAwarded = parseInt(question.querySelector('marks_awarded')?.textContent || '0');
            const marksPossible = parseInt(question.querySelector('marks_possible')?.textContent || '0');
            const percentage = marksPossible > 0 ? Math.round((marksAwarded / marksPossible) * 100) : 0;
            const feedback = question.querySelector('feedback')?.textContent?.trim() || '';

            // Extract criteria breakdown - simplified approach
            const marksBreakdown = question.querySelector('marks_breakdown');
            const criteriaBreakdown: CriterionBreakdown[] = [];

            if (marksBreakdown) {
                const criteria = Array.from(marksBreakdown.querySelectorAll('criterion'));

                criteria.forEach(criterion => {
                    const name = criterion.getAttribute('name') || '';
                    const score = criterion.textContent || '0';

                    // Skip "Total Possible" entries for now - keep it simple
                    if (!name.startsWith('Total Possible for ')) {
                        criteriaBreakdown.push({
                            criterion: name,
                            score: score,
                            maxScore: '0' // Will be updated if we find the corresponding total
                        });
                    }
                });

                // Try to match totals with criteria
                criteria.forEach(criterion => {
                    const name = criterion.getAttribute('name') || '';
                    const score = criterion.textContent || '0';

                    if (name.startsWith('Total Possible for ')) {
                        const baseName = name.replace('Total Possible for ', '');
                        const matchingCriterion = criteriaBreakdown.find(c => c.criterion === baseName);
                        if (matchingCriterion) {
                            matchingCriterion.maxScore = score;
                        }
                    }
                });
            }

            return {
                questionNumber,
                marksAwarded,
                marksPossible,
                percentage,
                feedback,
                criteriaBreakdown
            };
        });

        return {
            totalMarks,
            maxMarks,
            overallPercentage,
            questions
        };

    } catch (error) {
        console.error('Error parsing evaluation breakdown:', error);
        return null;
    }
};

// --- Display Component for Question Breakdown ---
const QuestionBreakdownDisplay: React.FC<{ evaluationData: EvaluationBreakdown | null }> = ({ evaluationData }) => {
    // Handle null or invalid data
    if (!evaluationData) {
        return (
            <div className="bg-card border border-border rounded-lg p-6 text-center">
                <p className="text-muted-foreground">Unable to load question breakdown data.</p>
            </div>
        );
    }
    const getPerformanceColor = (percentage: number): string => {
        if (percentage >= 80) return "text-primary";
        if (percentage >= 60) return "text-foreground";
        if (percentage >= 40) return "text-muted-foreground";
        return "text-destructive";
    };

    const getPerformanceBg = (percentage: number): string => {
        if (percentage >= 80) return "bg-primary/5 border-primary/20";
        if (percentage >= 60) return "bg-accent/30 border-border";
        if (percentage >= 40) return "bg-muted/50 border-border";
        return "bg-destructive/5 border-destructive/20";
    };

    const getPerformanceBadge = (percentage: number): string => {
        if (percentage >= 80) return "bg-primary/10 text-primary border-primary/20";
        if (percentage >= 60) return "bg-accent text-foreground border-border";
        if (percentage >= 40) return "bg-muted text-muted-foreground border-border";
        return "bg-destructive/10 text-destructive border-destructive/20";
    };

    return (
        <div className="space-y-4 lg:space-y-6 p-2 lg:p-0">
            {/* Overall Performance */}
            {/* <div className="bg-card border border-border rounded-lg p-6">
                <h2 className="text-lg font-semibold mb-4 text-foreground">Overall Performance</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                        <p className="text-sm text-muted-foreground mb-1">Total Score</p>
                        <p className="text-2xl font-semibold text-foreground">
                            {evaluationData.totalMarks}/{evaluationData.maxMarks}
                        </p>
                    </div>
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                        <p className="text-sm text-muted-foreground mb-1">Percentage</p>
                        <p className={`text-2xl font-semibold ${getPerformanceColor(evaluationData.overallPercentage)}`}>
                            {evaluationData.overallPercentage}%
                        </p>
                    </div>
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                        <p className="text-sm text-muted-foreground mb-1">Questions</p>
                        <p className="text-2xl font-semibold text-foreground">{evaluationData.questions.length}</p>
                    </div>
                </div>
            </div> */}

            {/* Individual Question Breakdown */}
            <div className="space-y-3 lg:space-y-4">
                {/* <h2 className="text-lg font-semibold text-foreground">Question-wise Breakdown</h2> */}
                {evaluationData.questions.map((question) => (
                    <div key={question.questionNumber} className="bg-card border border-border rounded-lg overflow-hidden">
                        {/* Question Header */}
                        <div className={`p-3 lg:p-4 border-b ${getPerformanceBg(question.percentage)}`}>
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0">
                                <div className="flex items-center gap-2 lg:gap-3">
                                    <h3 className="text-sm lg:text-base font-semibold text-foreground">
                                        {question.questionNumber}
                                    </h3>
                                    <span className={`px-2 py-1 rounded text-xs font-medium border ${getPerformanceBadge(question.percentage)}`}>
                                        {question.percentage}%
                                    </span>
                                </div>
                                <div className="text-left sm:text-right">
                                    <p className={`text-sm lg:text-base font-semibold ${getPerformanceColor(question.percentage)}`}>
                                        {question.marksAwarded}/{question.marksPossible}
                                    </p>
                                    <p className="text-xs text-muted-foreground">marks</p>
                                </div>
                            </div>
                        </div>

                        {/* Question Content */}
                        <div className="p-3 lg:p-4 space-y-3 lg:space-y-4">
                            {/* Criteria Breakdown */}
                            {question.criteriaBreakdown.length > 0 && (
                                <div>
                                    <h4 className="text-sm font-medium text-foreground mb-2">Marking Criteria</h4>
                                    <div className="space-y-2">
                                        {question.criteriaBreakdown.map((criterion, criterionIndex) => {
                                            const criterionPercentage = criterion.maxScore ?
                                                Math.round((parseInt(criterion.score) / parseInt(criterion.maxScore)) * 100) : 0;

                                            return (
                                                <div key={criterionIndex} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 p-3 bg-muted/30 rounded border border-border">
                                                    <span className="text-sm text-foreground flex-1 pr-0 sm:pr-3">
                                                        {criterion.criterion}
                                                    </span>
                                                    <div className="flex items-center gap-2 justify-start sm:justify-end">
                                                        <span className="text-sm font-medium text-foreground">
                                                            {criterion.score}/{criterion.maxScore}
                                                        </span>
                                                        <span className={`text-xs px-2 py-1 rounded font-medium ${getPerformanceBadge(criterionPercentage)}`}>
                                                            {criterionPercentage}%
                                                        </span>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}

                            {/* Feedback */}
                            {question.feedback && (
                                <div>
                                    <h4 className="text-sm font-medium text-foreground mb-2">Feedback</h4>
                                    <div className="p-3 bg-muted/30 border border-border rounded">
                                        <p className="text-sm text-foreground leading-relaxed break-words">
                                            {question.feedback}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default QuestionBreakdownDisplay;
