import React, { useEffect, useRef } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import BlobPdfViewer from '@/components/BlobPdfViewer';
import QuestionBreakdownDisplay from '@/components/QuestionBreakdown';
import DownloadStudentReport from '@/components/DownloadStudentReport';

interface QuestionBreakdownPageProps { }

const QuestionBreakdownPage: React.FC<QuestionBreakdownPageProps> = () => {
    const { submissionId } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const breakdownContentRef = useRef<HTMLDivElement>(null);

    // Get the submission data from location state or fetch it
    const submissionData = location.state?.submissionData;

    useEffect(() => {
        // If no submission data is passed, we might need to fetch it
        if (!submissionData && submissionId) {
            // TODO: Fetch submission data by ID if needed
            console.log('Fetching submission data for ID:', submissionId);
        }
    }, [submissionId, submissionData]);

    if (!submissionData) {
        return (
            <div className="min-h-screen bg-background flex items-center justify-center">
                <div className="text-center">
                    <h2 className="text-xl font-semibold text-foreground mb-2">Loading...</h2>
                    <p className="text-muted-foreground">Fetching submission details</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-background p-2 pb-16">
            {/* Header */}
            <div className="border-b border-border py-3">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    {/* Top row - Back button and title */}
                    <div className="flex items-center gap-3 lg:gap-4">
                        <button
                            onClick={() => navigate(-1)}
                            className="flex items-center gap-2 p-2 rounded-md text-muted-foreground hover:bg-muted transition-all duration-200"
                        >
                            <ArrowLeftIcon className="w-4 h-4" />
                            <span className="text-sm font-medium hidden sm:inline">Back</span>
                        </button>
                        <div className="h-6 w-px bg-border hidden sm:block" />
                        <h1 className="text-lg sm:text-xl lg:text-2xl font-semibold font-['Space_Grotesk'] text-foreground">Question Breakdown</h1>
                    </div>

                    {/* Bottom row - Student info and download */}
                    <div className="flex items-center justify-between lg:justify-end gap-3 lg:gap-6">
                        {/* Download button */}
                        <DownloadStudentReport
                            submissionData={submissionData}
                            breakdownContentRef={breakdownContentRef}
                        />

                        {/* Student info */}
                        <div className="flex items-center gap-3 lg:gap-6">
                            <div className="flex flex-col items-end">
                                <span className="text-sm lg:text-md font-medium text-foreground truncate max-w-[120px] sm:max-w-none">{submissionData.studentName}</span>
                                <span className="text-xs lg:text-sm text-muted-foreground">Roll: {submissionData.rollNumber}</span>
                            </div>
                            <div className="h-6 lg:h-8 w-px bg-border" />
                            <div className="flex flex-col items-end">
                                <span className="text-sm lg:text-md font-semibold text-foreground">
                                    {submissionData.totalMarks}/{submissionData.maxMarks}
                                </span>
                                <span className="text-xs lg:text-sm text-muted-foreground">
                                    {submissionData.percentage}% Score
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Debug Toggle - uncomment if needed */}
                {/* {submissionData.pdfUrl && (
                    <button
                        onClick={() => setShowDebugViewer(!showDebugViewer)}
                        className="px-3 py-1.5 text-sm bg-muted text-muted-foreground rounded hover:bg-accent transition-colors"
                    >
                        {showDebugViewer ? 'Normal View' : 'Debug View'}
                    </button>
                )} */}
            </div>


            {/* Main Content - Responsive Layout */}
            <div className="flex flex-col lg:flex-row min-h-[calc(100dvh-120px)] lg:h-[calc(100dvh-90px)]">
                {/* PDF Viewer - Top on mobile, Left on desktop */}
                {submissionData.pdfUrl && (
                    <div className="w-full lg:w-1/2 h-[40vh] sm:h-[50vh] lg:h-full lg:border-r border-b lg:border-b-0 border-border">
                        <div className="h-full p-1 sm:p-2 lg:p-1">
                            <div className="h-full border border-border rounded-lg overflow-hidden">
                                <BlobPdfViewer
                                    s3Key={submissionData.pdfUrl || ''}
                                    title={`Answer sheet for ${submissionData.studentName}`}
                                    className="w-full h-full"
                                />
                            </div>
                        </div>
                    </div>
                )}

                {/* Question Analysis - Bottom on mobile, Right on desktop */}
                <div className={`${submissionData.pdfUrl ? 'w-full lg:w-1/2' : 'w-full'} ${submissionData.pdfUrl ? 'h-[60vh] lg:h-full' : 'min-h-[calc(100dvh-120px)]'} bg-background`}>
                    <div className="h-full p-1 sm:p-2 lg:p-1">
                        <div className="h-full overflow-y-auto" ref={breakdownContentRef}>
                            <QuestionBreakdownDisplay evaluationData={submissionData.detailedBreakdown} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default QuestionBreakdownPage;
